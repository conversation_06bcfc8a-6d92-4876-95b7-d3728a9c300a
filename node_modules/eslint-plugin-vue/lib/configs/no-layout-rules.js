/*
 * IMPORTANT!
 * This file has been automatically generated,
 * in order to update its content execute "npm run update"
 */
module.exports = {
  rules: {
    'vue/array-bracket-newline': 'off',
    'vue/array-bracket-spacing': 'off',
    'vue/array-element-newline': 'off',
    'vue/arrow-spacing': 'off',
    'vue/block-spacing': 'off',
    'vue/block-tag-newline': 'off',
    'vue/brace-style': 'off',
    'vue/comma-dangle': 'off',
    'vue/comma-spacing': 'off',
    'vue/comma-style': 'off',
    'vue/define-macros-order': 'off',
    'vue/dot-location': 'off',
    'vue/first-attribute-linebreak': 'off',
    'vue/func-call-spacing': 'off',
    'vue/html-closing-bracket-newline': 'off',
    'vue/html-closing-bracket-spacing': 'off',
    'vue/html-comment-content-newline': 'off',
    'vue/html-comment-content-spacing': 'off',
    'vue/html-comment-indent': 'off',
    'vue/html-indent': 'off',
    'vue/html-quotes': 'off',
    'vue/html-self-closing': 'off',
    'vue/key-spacing': 'off',
    'vue/keyword-spacing': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/max-len': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/multiline-ternary': 'off',
    'vue/mustache-interpolation-spacing': 'off',
    'vue/new-line-between-multi-line-property': 'off',
    'vue/no-extra-parens': 'off',
    'vue/no-multi-spaces': 'off',
    'vue/no-spaces-around-equal-signs-in-attribute': 'off',
    'vue/object-curly-newline': 'off',
    'vue/object-curly-spacing': 'off',
    'vue/object-property-newline': 'off',
    'vue/operator-linebreak': 'off',
    'vue/padding-line-between-blocks': 'off',
    'vue/padding-line-between-tags': 'off',
    'vue/padding-lines-in-component-definition': 'off',
    'vue/quote-props': 'off',
    'vue/script-indent': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/space-in-parens': 'off',
    'vue/space-infix-ops': 'off',
    'vue/space-unary-ops': 'off',
    'vue/template-curly-spacing': 'off',
    'vue/v-for-delimiter-style': 'off'
  }
}
