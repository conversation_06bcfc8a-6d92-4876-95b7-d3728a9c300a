const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8000;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const mockWords = [
  {
    id: 1,
    word: 'Hallo',
    chinese: '你好',
    english: 'Hello',
    wordType: 'Interjektion',
    phase: 'selection',
    options: [
      { chinese: '你好', english: 'Hello', isCorrect: true },
      { chinese: '再见', english: 'Goodbye', isCorrect: false },
      { chinese: '谢谢', english: 'Thank you', isCorrect: false },
      { chinese: '对不起', english: 'Sorry', isCorrect: false }
    ]
  },
  {
    id: 2,
    word: 'Danke',
    chinese: '谢谢',
    english: 'Thank you',
    wordType: 'Interjektion',
    phase: 'recognition'
  }
];

let currentWordIndex = 0;
let sessionActive = false;

// API路由
app.post('/api/login', (req, res) => {
  const { password } = req.body;
  
  if (password === 'admin' || password === '123456') {
    res.json({
      token: 'mock-jwt-token-' + Date.now(),
      user: { id: 1, name: 'Test User' }
    });
  } else {
    res.status(401).json({ message: '密码错误' });
  }
});

app.get('/api/stats', (req, res) => {
  res.json({
    totalWords: 100,
    learnedWords: 25,
    reviewWords: 15,
    todayLearned: 8
  });
});

app.post('/api/session/start', (req, res) => {
  sessionActive = true;
  currentWordIndex = 0;
  res.json({
    sessionId: 'session-' + Date.now(),
    message: '学习会话已开始'
  });
});

app.get('/api/session/current', (req, res) => {
  if (!sessionActive) {
    return res.status(400).json({ message: '没有活跃的学习会话' });
  }
  
  if (currentWordIndex >= mockWords.length) {
    return res.json({ message: '没有更多单词了', isLastWord: true });
  }
  
  const word = { 
    ...mockWords[currentWordIndex],
    currentIndex: currentWordIndex + 1,
    totalCount: mockWords.length
  };
  
  res.json(word);
});

app.post('/api/session/answer', (req, res) => {
  const { type, isCorrect } = req.body;
  
  // 模拟答案处理
  currentWordIndex++;
  
  res.json({
    correct: isCorrect,
    isLastWord: currentWordIndex >= mockWords.length,
    message: isCorrect ? '回答正确!' : '回答错误，继续加油!'
  });
});

app.post('/api/session/end', (req, res) => {
  sessionActive = false;
  currentWordIndex = 0;
  res.json({
    message: '学习会话已结束',
    wordsLearned: Math.min(currentWordIndex, mockWords.length)
  });
});

app.get('/api/review/list', (req, res) => {
  const reviewWords = mockWords.slice(0, 3).map(word => ({
    ...word,
    reviewDate: new Date().toISOString(),
    difficulty: 'normal'
  }));
  
  res.json(reviewWords);
});

app.post('/api/review/submit', (req, res) => {
  const { wordId, result } = req.body;
  
  res.json({
    success: true,
    message: '复习结果已提交',
    nextReviewDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  });
});

app.get('/api/progress', (req, res) => {
  res.json({
    totalProgress: 25,
    weeklyProgress: [
      { day: 'Mon', learned: 5 },
      { day: 'Tue', learned: 3 },
      { day: 'Wed', learned: 7 },
      { day: 'Thu', learned: 4 },
      { day: 'Fri', learned: 6 },
      { day: 'Sat', learned: 0 },
      { day: 'Sun', learned: 0 }
    ]
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ message: '接口不存在' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 模拟后端服务器启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔑 测试密码: admin 或 123456`);
  console.log(`📚 可用接口:`);
  console.log(`   POST /api/login - 登录`);
  console.log(`   GET  /api/stats - 获取统计`);
  console.log(`   POST /api/session/start - 开始学习`);
  console.log(`   GET  /api/session/current - 获取当前单词`);
  console.log(`   POST /api/session/answer - 提交答案`);
  console.log(`   GET  /api/review/list - 获取复习列表`);
  console.log(`=====================================`);
});
